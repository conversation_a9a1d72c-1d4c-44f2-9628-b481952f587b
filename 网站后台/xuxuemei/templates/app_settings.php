<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}

// 获取当前选中的标签页
$current_tab = $_GET['tab'] ?? 'popup';
?>

<div class="app-settings-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1><i class="fas fa-mobile-alt"></i> APP设置</h1>
        <p>管理APP弹窗、协议、知识库和版本更新</p>
    </div>

    <!-- 顶部导航标签 -->
    <div class="app-settings-nav">
        <div class="nav-tabs">
            <button class="nav-tab <?php echo $current_tab === 'popup' ? 'active' : ''; ?>" 
                    onclick="switchTab('popup')" data-tab="popup">
                <i class="fas fa-window-maximize"></i>
                <span>APP弹窗</span>
            </button>
            <button class="nav-tab <?php echo $current_tab === 'agreement' ? 'active' : ''; ?>" 
                    onclick="switchTab('agreement')" data-tab="agreement">
                <i class="fas fa-file-contract"></i>
                <span>APP协议</span>
            </button>

            <button class="nav-tab <?php echo $current_tab === 'update' ? 'active' : ''; ?>" 
                    onclick="switchTab('update')" data-tab="update">
                <i class="fas fa-download"></i>
                <span>APP更新</span>
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="app-settings-content">
        <!-- APP弹窗模块 -->
        <div id="popup-content" class="tab-content <?php echo $current_tab === 'popup' ? 'active' : ''; ?>">
            <?php include 'app_settings/popup_module.php'; ?>
        </div>

        <!-- APP协议模块 -->
        <div id="agreement-content" class="tab-content <?php echo $current_tab === 'agreement' ? 'active' : ''; ?>">
            <?php include 'app_settings/agreement_module.php'; ?>
        </div>



        <!-- APP更新模块 -->
        <div id="update-content" class="tab-content <?php echo $current_tab === 'update' ? 'active' : ''; ?>">
            <?php include 'app_settings/update_module.php'; ?>
        </div>
    </div>
</div>

<!-- APP设置专用样式 -->
<style>
.app-settings-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    color: white;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.page-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
}

/* 顶部导航标签 */
.app-settings-nav {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 8px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-tabs {
    display: flex;
    gap: 4px;
}

.nav-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.nav-tab.active {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.nav-tab i {
    font-size: 16px;
}

/* 内容区域 */
.app-settings-content {
    position: relative;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 通用按钮样式 */
.btn {
    padding: 12px 25px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 14px;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    color: white;
    border-color: transparent;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c, #ff4b2b);
    color: white;
    border-color: transparent;
}

.btn-warning {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
    color: white;
    border-color: transparent;
}

.btn-info {
    background: linear-gradient(135deg, #36d1dc, #5b86e5);
    color: white;
    border-color: transparent;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Toast样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    color: #333;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 10px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.toast-success {
    border-left: 4px solid #28a745;
}

.toast.toast-error {
    border-left: 4px solid #dc3545;
}

.toast.toast-warning {
    border-left: 4px solid #ffc107;
}

.toast.toast-info {
    border-left: 4px solid #17a2b8;
}

/* 确认对话框样式 */
.confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.confirm-modal.show {
    opacity: 1;
}

.confirm-content {
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    border-radius: 15px;
    max-width: 400px;
    width: 90%;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.confirm-modal.show .confirm-content {
    transform: scale(1);
}

.confirm-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 10px;
}

.confirm-header h3 {
    color: white;
    margin: 0;
    font-size: 18px;
}

.confirm-header i {
    color: #ffc107;
    font-size: 20px;
}

.confirm-body {
    padding: 20px;
}

.confirm-body p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    line-height: 1.5;
}

.confirm-footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-settings-container {
        padding: 15px;
    }
    
    .nav-tabs {
        flex-direction: column;
        gap: 8px;
    }
    
    .nav-tab {
        padding: 12px 15px;
    }
    
    .page-header h1 {
        font-size: 24px;
    }
    
    .toast {
        right: 10px;
        left: 10px;
        width: auto;
    }
}
</style>

<!-- APP设置专用JavaScript -->
<script>
// 标签切换功能
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 激活当前标签
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-content`).classList.add('active');
    
    // 更新URL参数
    const url = new URL(window.location);
    url.searchParams.set('tab', tabName);
    window.history.pushState({}, '', url);
    
    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('tabChanged', { detail: { tab: tabName } }));
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('APP设置页面加载完成');
    
    // 监听浏览器后退/前进按钮
    window.addEventListener('popstate', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab') || 'popup';
        switchTab(tab);
    });
});

// 通用提示函数
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => toast.classList.add('show'), 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
}

// 通用确认对话框
function showConfirm(message, callback) {
    const modal = document.createElement('div');
    modal.className = 'confirm-modal';
    modal.innerHTML = `
        <div class="confirm-content">
            <div class="confirm-header">
                <i class="fas fa-question-circle"></i>
                <h3>确认操作</h3>
            </div>
            <div class="confirm-body">
                <p>${message}</p>
            </div>
            <div class="confirm-footer">
                <button class="btn btn-secondary" onclick="closeConfirm()">取消</button>
                <button class="btn btn-danger" onclick="confirmAction()">确认</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    window.confirmCallback = callback;
    window.confirmModal = modal;
    
    setTimeout(() => modal.classList.add('show'), 100);
}

function closeConfirm() {
    if (window.confirmModal) {
        window.confirmModal.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(window.confirmModal);
            window.confirmModal = null;
            window.confirmCallback = null;
        }, 300);
    }
}

function confirmAction() {
    if (window.confirmCallback) {
        window.confirmCallback();
    }
    closeConfirm();
}
</script>
