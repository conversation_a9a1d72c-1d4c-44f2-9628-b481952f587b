<?php
// 不要在这里设置header，因为index.php已经输出了内容
$message = '';

// 脚本处理函数 - 只提取信息，不修改代码
function processUserScript($code) {
    $result = [
        'name' => '',
        'version' => '1.0.0',
        'code' => $code  // 保留完整的原始代码
    ];

    // 检查是否包含userscript头部
    if (strpos($code, '// ==UserScript==') !== false) {
        // 提取脚本名称
        if (preg_match('/@name\s+(.+)/i', $code, $matches)) {
            $result['name'] = trim($matches[1]);
        }

        // 提取版本号
        if (preg_match('/@version\s+(.+)/i', $code, $matches)) {
            $result['version'] = trim($matches[1]);
        }

        // 不再自动清理头部信息，保留完整代码
        $result['code'] = $code;
    }

    return $result;
}





// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_script'])) {
        $script_id = $_POST['script_id'] ?? null;
        $name = trim($_POST['name']);
        $version = trim($_POST['version']);
        $description = trim($_POST['description'] ?? '');
        $code = $_POST['script_code'];

        // 权限设置 - 修复：正确处理隐藏输入框的值
        $has_wechat_store = (isset($_POST['has_wechat_store']) && $_POST['has_wechat_store'] == '1') ? 1 : 0;
        $has_douyin_store = (isset($_POST['has_douyin_store']) && $_POST['has_douyin_store'] == '1') ? 1 : 0;

        // 如果名称为空，尝试自动处理脚本
        if (empty($name) && !empty($code)) {
            $processed = processUserScript($code);
            $name = $processed['name'];
            $version = $processed['version'];
            $code = $processed['code'];
        }

        // 验证必填字段
        if (empty($name) || empty($version) || empty($code)) {
            $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 脚本名称、版本和代码都不能为空</div>";
        } else {
            // 首先确保数据库表结构正确
            try {
                // 检查并添加缺失的字段（使用传统方法，兼容所有MySQL版本）
                try {
                    $pdo->exec("ALTER TABLE scripts ADD COLUMN script_code longtext DEFAULT NULL");
                } catch (Exception $e2) {
                    // 字段已存在，忽略错误
                }
                try {
                    $pdo->exec("ALTER TABLE scripts ADD COLUMN has_wechat_store tinyint(1) DEFAULT 0");
                } catch (Exception $e2) {
                    // 字段已存在，忽略错误
                }
                try {
                    $pdo->exec("ALTER TABLE scripts ADD COLUMN has_douyin_store tinyint(1) DEFAULT 0");
                } catch (Exception $e2) {
                    // 字段已存在，忽略错误
                }

                // 移除有问题的批量UPDATE语句，避免影响现有脚本数据
                // 原来的语句: UPDATE scripts SET script_code = code WHERE script_code IS NULL AND code IS NOT NULL
                // 这个语句会在每次添加脚本时执行，可能导致数据覆盖问题
            } catch (Exception $e) {
                // 忽略表结构错误，继续执行
            }

            // 检查表结构，确定可用的字段
            $table_columns = [];
            try {
                $stmt_check = $pdo->query("DESCRIBE scripts");
                $columns = $stmt_check->fetchAll();
                foreach ($columns as $col) {
                    $table_columns[] = $col['Field'];
                }
            } catch (Exception $e) {
                // 如果无法获取表结构，使用基本字段
                $table_columns = ['id', 'name', 'version', 'description', 'created_at'];
            }

            // 构建动态SQL语句
            $update_fields = ['name = ?', 'version = ?', 'description = ?'];
            $insert_fields = ['name', 'version', 'description'];
            $insert_placeholders = ['?', '?', '?'];
            $params = [$name, $version, $description];

            // 添加代码字段 - 简化逻辑，确保数据一致性
            // 如果script_code字段存在，使用它作为主要字段
            if (in_array('script_code', $table_columns)) {
                $update_fields[] = 'script_code = ?';
                $insert_fields[] = 'script_code';
                $insert_placeholders[] = '?';
                $params[] = $code;
            }

            // 如果code字段存在，也要更新它以保持兼容性
            if (in_array('code', $table_columns)) {
                $update_fields[] = 'code = ?';
                $insert_fields[] = 'code';
                $insert_placeholders[] = '?';
                $params[] = $code;
            }

            // 添加权限字段
            if (in_array('has_wechat_store', $table_columns)) {
                $update_fields[] = 'has_wechat_store = ?';
                $insert_fields[] = 'has_wechat_store';
                $insert_placeholders[] = '?';
                $params[] = $has_wechat_store;
            }
            if (in_array('has_douyin_store', $table_columns)) {
                $update_fields[] = 'has_douyin_store = ?';
                $insert_fields[] = 'has_douyin_store';
                $insert_placeholders[] = '?';
                $params[] = $has_douyin_store;
            }

            // 使用事务处理确保数据一致性
            try {
                $pdo->beginTransaction();

                if ($script_id) { // 更新
                    $update_sql = "UPDATE scripts SET " . implode(', ', $update_fields) . " WHERE id = ?";
                    $update_params = array_merge($params, [$script_id]);

                    $stmt = $pdo->prepare($update_sql);
                    if (!$stmt->execute($update_params)) {
                        throw new Exception("更新脚本失败");
                    }

                    $pdo->commit();
                    // 更新成功后重定向到脚本列表页面，退出编辑状态
                    header("Location: index.php?page=scripts&updated=1");
                    exit();

                } else { // 新增
                    $insert_sql = "INSERT INTO scripts (" . implode(', ', $insert_fields) . ") VALUES (" . implode(', ', $insert_placeholders) . ")";

                    $stmt = $pdo->prepare($insert_sql);
                    if (!$stmt->execute($params)) {
                        throw new Exception("添加脚本失败");
                    }

                    $pdo->commit();
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已成功添加！</div>";
                }

            } catch (Exception $e) {
                $pdo->rollBack();
                $error_msg = $script_id ? "更新脚本失败" : "添加脚本失败";
                $message = "<div class='message error'><i class='fas fa-times-circle'></i> {$error_msg}: " . $e->getMessage() . "</div>";
                error_log("脚本操作失败: " . $e->getMessage());
            }
        }
    }
}

// 删除脚本
if (isset($_GET['delete_script'])) {
    $script_id = $_GET['delete_script'];
    
    // 检查是否有卡密关联此脚本
    $stmt_check = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE script_id = ?");
    $stmt_check->execute([$script_id]);
    $linked_keys = $stmt_check->fetchColumn();
    
    if ($linked_keys > 0) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 无法删除：还有 {$linked_keys} 个卡密关联此脚本</div>";
    } else {
        $stmt = $pdo->prepare("DELETE FROM scripts WHERE id = ?");
        if ($stmt->execute([$script_id])) {
            $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已删除！</div>";
        }
    }
}

// 检查是否有更新成功的提示
if (isset($_GET['updated']) && $_GET['updated'] == '1') {
    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已成功更新！</div>";
}

// 获取要编辑的脚本
$edit_script = null;
if (isset($_GET['edit_script'])) {
    $stmt = $pdo->prepare("SELECT * FROM scripts WHERE id = ?");
    $stmt->execute([$_GET['edit_script']]);
    $edit_script = $stmt->fetch(PDO::FETCH_ASSOC);

    // 修复：确保正确获取脚本代码字段
    if ($edit_script) {
        // 优先使用script_code字段，如果为空或不存在则使用code字段
        if (empty($edit_script['script_code']) && !empty($edit_script['code'])) {
            $edit_script['script_code'] = $edit_script['code'];
        }
        // 如果script_code字段不存在但code字段存在，创建script_code字段
        if (!isset($edit_script['script_code']) && isset($edit_script['code'])) {
            $edit_script['script_code'] = $edit_script['code'];
        }
    }
}

// 获取脚本列表 - 包含状态字段和权限字段，兼容不同的数据库结构
// 首先检查表结构，确定可用的字段
$table_columns = [];
try {
    $stmt = $pdo->query("DESCRIBE scripts");
    $columns = $stmt->fetchAll();
    foreach ($columns as $col) {
        $table_columns[] = $col['Field'];
    }
} catch (Exception $e) {
    // 如果无法获取表结构，使用基本字段
    $table_columns = ['id', 'name', 'version', 'description', 'created_at'];
}

// 根据可用字段构建查询
$script_code_field = 'NULL as script_code';
if (in_array('script_code', $table_columns)) {
    $script_code_field = 's.script_code';
} elseif (in_array('code', $table_columns)) {
    $script_code_field = 's.code as script_code';
}

$has_wechat_store_field = in_array('has_wechat_store', $table_columns) ? 's.has_wechat_store' : '0 as has_wechat_store';
$has_douyin_store_field = in_array('has_douyin_store', $table_columns) ? 's.has_douyin_store' : '0 as has_douyin_store';
$status_field = in_array('status', $table_columns) ? 's.status' : "'active' as status";

$scripts_sql = "
    SELECT s.id,
           s.name,
           s.version,
           s.description,
           {$script_code_field},
           {$status_field},
           {$has_wechat_store_field},
           {$has_douyin_store_field},
           s.created_at,
           (SELECT COUNT(*) FROM license_keys WHERE script_id = s.id) as linked_keys_count
    FROM scripts s
    ORDER BY s.id DESC
";
$scripts = $pdo->query($scripts_sql)->fetchAll(PDO::FETCH_ASSOC);

// 确保每个脚本都有默认值
foreach ($scripts as &$script) {
    $script['name'] = $script['name'] ?? '';
    $script['version'] = $script['version'] ?? '1.0.0';
    $script['description'] = $script['description'] ?? '';

    $script['has_wechat_store'] = $script['has_wechat_store'] ?? 0;
    $script['has_douyin_store'] = $script['has_douyin_store'] ?? 0;
    $script['linked_keys_count'] = isset($script['linked_keys_count']) ? (int)$script['linked_keys_count'] : 0;
    $script['created_at'] = $script['created_at'] ?? date('Y-m-d H:i:s');

    // 修复：确保script_code字段正确处理
    if (empty($script['script_code']) && !empty($script['code'])) {
        $script['script_code'] = $script['code'];
    }
    if (!isset($script['script_code'])) {
        $script['script_code'] = '';
    }
}
// 重要：断开引用传递，避免后续使用$script变量时影响数组最后一个元素
unset($script);
?>

<style>
    /* 脚本管理页面专用样式 */
    .scripts-container {
        max-width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
    }
    
    .card {
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 15px !important;
        padding: 20px !important;
        margin-bottom: 20px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    }
    
    .card h2 {
        color: white !important;
        margin-bottom: 20px !important;
        font-size: 18px !important;
        font-weight: 600 !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
    }
    
    .form-inline {
        display: flex !important;
        flex-direction: column !important;
        gap: 0 !important;
        margin-bottom: 20px !important;
    }
    
    @media (max-width: 768px) {
        .form-inline {
            flex-direction: column !important;
        }
    }
    
    .form-group {
        margin-bottom: 20px !important;
    }
    
    .form-group label {
        display: block !important;
        color: white !important;
        margin-bottom: 8px !important;
        font-weight: 500 !important;
        font-size: 14px !important;
    }
    
    .form-group input,
    .form-group textarea {
        width: 100% !important;
        padding: 12px 15px !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 8px !important;
        color: white !important;
        font-size: 14px !important;
        transition: all 0.3s ease !important;
    }
    
    .form-group input::placeholder,
    .form-group textarea::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
    }
    
    .form-group input:focus,
    .form-group textarea:focus {
        outline: none !important;
        border-color: #ff6b9d !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2) !important;
    }
    
    .table-container {
        overflow-x: auto !important;
        border-radius: 8px !important;
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
    
    table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 14px !important;
    }
    
    table th {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
        padding: 15px 12px !important;
        text-align: left !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        white-space: nowrap !important;
    }
    
    table td {
        padding: 15px 12px !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        vertical-align: middle !important;
        font-size: 13px !important;
    }
    
    table tr:hover {
        background: rgba(255, 255, 255, 0.05) !important;
    }

    .version-badge, .count-badge {
        display: inline-block !important;
        padding: 6px 12px !important;
        border-radius: 12px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }


    
    .btn {
        display: inline-flex !important;
        align-items: center !important;
        gap: 8px !important;
        padding: 10px 20px !important;
        border: none !important;
        border-radius: 8px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-decoration: none !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
        color: white !important;
    }
    
    .btn-secondary {
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }
    
    .btn-success {
        background: linear-gradient(135deg, #4ade80, #22c55e) !important;
        color: white !important;
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #fbbf24, #f59e0b) !important;
        color: white !important;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #f87171, #ef4444) !important;
        color: white !important;
    }
    
    .btn:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    }
    
    .message {
        padding: 15px 20px !important;
        border-radius: 8px !important;
        margin-bottom: 20px !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
    }
    
    .message.success {
        background: rgba(34, 197, 94, 0.2) !important;
        border: 1px solid rgba(34, 197, 94, 0.3) !important;
        color: #4ade80 !important;
    }
    
    .message.error {
        background: rgba(239, 68, 68, 0.2) !important;
        border: 1px solid rgba(239, 68, 68, 0.3) !important;
        color: #f87171 !important;
    }

    /* 全新的权限卡片样式 */
    .permission-cards-wrapper {
        display: flex;
        gap: 20px;
        margin-top: 15px;
        flex-wrap: wrap;
    }

    .permission-card-item {
        width: 180px;
        height: 180px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        z-index: 10;
        pointer-events: auto;
    }

    .permission-card-box {
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        transition: all 0.3s ease;
        pointer-events: auto;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }



    .permission-card-item.selected .permission-card-box {
        background: rgba(138, 43, 226, 0.3);
        border-color: rgba(138, 43, 226, 0.6);
        box-shadow: 0 0 20px rgba(138, 43, 226, 0.4);
    }

    .permission-card-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        line-height: 1.2;
        pointer-events: none;
        user-select: none;
    }

    .permission-card-subtitle {
        color: white;
        font-size: 16px;
        font-weight: 500;
        opacity: 0.9;
        line-height: 1.2;
        pointer-events: none;
        user-select: none;
    }

    /* 权限徽章样式 */
    .permission-badges {
        display: flex !important;
        flex-direction: column !important;
        gap: 4px !important;
        align-items: center !important;
    }

    .permission-badge {
        display: inline-flex !important;
        align-items: center !important;
        gap: 4px !important;
        padding: 2px 8px !important;
        border-radius: 12px !important;
        font-size: 11px !important;
        font-weight: 500 !important;
        white-space: nowrap !important;
        color: white !important;
    }

    .permission-badge.wechat-store {
        background: linear-gradient(135deg, #07c160, #00a854) !important;
        border: 1px solid rgba(7, 193, 96, 0.3) !important;
    }

    .permission-badge.douyin-store {
        background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
        border: 1px solid rgba(255, 107, 53, 0.3) !important;
    }

    .permission-badge i {
        font-size: 10px !important;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .permission-cards-wrapper {
            flex-direction: column;
            align-items: center;
        }

        .permission-card-item {
            width: 160px;
            height: 160px;
        }

        .permission-badges {
            flex-direction: row !important;
            flex-wrap: wrap !important;
            justify-content: center !important;
        }

        .permission-badge {
            font-size: 10px !important;
            padding: 1px 6px !important;
        }
    }
    
    .script-description {
        font-size: 12px !important;
        opacity: 0.7 !important;
        margin-top: 3px !important;
        line-height: 1.3 !important;
    }
    
    .script-name {
        font-weight: 600 !important;
        color: white !important;
        margin-bottom: 3px !important;
        font-size: 14px !important;
    }
    
    .action-buttons {
        display: flex !important;
        gap: 8px !important;
        justify-content: center !important;
        flex-wrap: wrap !important;
    }
    
    .action-buttons .btn {
        padding: 8px 12px !important;
        font-size: 12px !important;
        min-width: 36px !important;
    }
    
    .auto-process-info {
        background: rgba(52, 152, 219, 0.2) !important;
        border: 1px solid rgba(52, 152, 219, 0.3) !important;
        color: white !important;
        padding: 15px 20px !important;
        border-radius: 8px !important;
        margin-bottom: 20px !important;
        font-size: 14px !important;
    }

    /* URL匹配功能样式 */
    .url-patterns-container {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 8px !important;
        padding: 15px !important;
    }

    .url-pattern-info {
        background: rgba(46, 204, 113, 0.2) !important;
        border: 1px solid rgba(46, 204, 113, 0.3) !important;
        color: white !important;
        padding: 10px 15px !important;
        border-radius: 6px !important;
        margin-bottom: 15px !important;
        font-size: 13px !important;
    }

    .url-pattern-item {
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
        margin-bottom: 10px !important;
    }

    .url-pattern-item input {
        flex: 1 !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        padding: 10px 15px !important;
        border-radius: 6px !important;
        font-size: 14px !important;
    }

    .url-pattern-item input:focus {
        outline: none !important;
        border-color: rgba(52, 152, 219, 0.5) !important;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2) !important;
    }

    .btn-remove-url {
        background: rgba(231, 76, 60, 0.8) !important;
        border: none !important;
        color: white !important;
        padding: 8px 12px !important;
        border-radius: 4px !important;
        cursor: pointer !important;
        font-size: 12px !important;
        transition: all 0.3s ease !important;
    }

    .btn-remove-url:hover {
        background: rgba(231, 76, 60, 1) !important;
        transform: scale(1.05) !important;
    }

    /* 筛选按钮样式 */
    .filter-btn {
        transition: all 0.3s ease;
    }

    .filter-btn:hover {
        background: rgba(0, 123, 255, 0.6) !important;
        color: white !important;
        border: none !important;
    }

    .filter-btn.active {
        background: rgba(0, 123, 255, 0.8) !important;
        color: white !important;
        border: none !important;
    }
</style>

<!-- 独立的测试脚本 -->
<script>
// 权限卡片初始化函数 - 单选模式
function initPermissionCards() {
    const cards = document.querySelectorAll('.permission-card-item');

    if (cards.length === 0) {
        console.log('未找到权限卡片');
        return;
    }

    console.log('初始化权限卡片，找到', cards.length, '个卡片');

    // 为每个卡片添加点击事件 - 单选模式
    cards.forEach((card, index) => {
        console.log('绑定卡片', index + 1, '事件:', card.getAttribute('data-permission'));

        card.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const permission = this.getAttribute('data-permission');
            console.log('点击权限卡片:', permission);

            // 检查当前卡片是否已选中
            const isCurrentlySelected = this.classList.contains('selected');

            // 单选模式：先取消所有卡片的选中状态
            cards.forEach(c => c.classList.remove('selected'));

            // 重置所有隐藏输入框
            const wechatInput = document.getElementById('has_wechat_store');
            const douyinInput = document.getElementById('has_douyin_store');
            if (wechatInput) wechatInput.value = '0';
            if (douyinInput) douyinInput.value = '0';

            // 如果当前卡片之前没有选中，则选中它
            if (!isCurrentlySelected) {
                this.classList.add('selected');

                // 更新对应的隐藏输入框
                const inputId = permission === 'wechat_store' ? 'has_wechat_store' : 'has_douyin_store';
                const input = document.getElementById(inputId);
                if (input) {
                    input.value = '1';
                    console.log('设置', inputId, '为 1');
                }
            }

            console.log('权限选择完成:', permission, '选中状态:', !isCurrentlySelected);
        });
    });
}

console.log('脚本管理功能已加载 - 支持自动提取脚本信息');
console.log('当前模式:', <?php echo $edit_script ? '"编辑模式"' : '"新增模式"'; ?>);

// 脚本筛选功能 - 全局函数
window.filterScripts = function(type) {
    const rows = document.querySelectorAll('.script-row');
    const filterButtons = document.querySelectorAll('.filter-btn');
    let visibleCount = 0;

    // 更新按钮状态
    filterButtons.forEach(btn => {
        btn.classList.remove('active');
        btn.style.background = 'rgba(255, 255, 255, 0.1)';
        btn.style.color = 'rgba(255,255,255,0.8)';
        btn.style.border = '1px solid rgba(255, 255, 255, 0.3)';
    });

    const activeBtn = document.getElementById('filter' + type.charAt(0).toUpperCase() + type.slice(1));
    if (activeBtn) {
        activeBtn.classList.add('active');
        activeBtn.style.background = 'rgba(0, 123, 255, 0.8)';
        activeBtn.style.color = 'white';
        activeBtn.style.border = 'none';
    }

    // 筛选脚本行
    rows.forEach(row => {
        let shouldShow = false;

        if (type === 'all') {
            shouldShow = true;
        } else if (type === 'wechat') {
            shouldShow = row.getAttribute('data-wechat') === '1';
        } else if (type === 'douyin') {
            shouldShow = row.getAttribute('data-douyin') === '1';
        }

        if (shouldShow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // 更新计数
    const countElement = document.getElementById('scriptCount');
    if (countElement) {
        countElement.textContent = visibleCount;
    }

    console.log('筛选完成:', type, '显示', visibleCount, '个脚本');
};



// 自动提取脚本信息的函数
function extractScriptInfo(code) {
    const result = {
        name: '',
        version: ''
    };

    if (!code || typeof code !== 'string') {
        return result;
    }

    // 提取脚本名称
    const nameMatch = code.match(/@name\s+(.+)/i);
    if (nameMatch) {
        result.name = nameMatch[1].trim();
    }

    // 提取版本号
    const versionMatch = code.match(/@version\s+(.+)/i);
    if (versionMatch) {
        result.version = versionMatch[1].trim();
    }

    return result;
}

// 更新表单字段（仅在新增模式下使用）
function updateFormFields(name, version) {
    // 检查是否为编辑模式，如果是则不执行自动填充
    const isEditMode = <?php echo $edit_script ? 'true' : 'false'; ?>;
    if (isEditMode) {
        console.log('编辑模式：跳过自动填充，保护现有脚本信息');
        return;
    }

    const nameInput = document.getElementById('scriptName');
    const versionInput = document.getElementById('scriptVersion');

    // 只有在字段为空时才自动填充，避免覆盖用户已输入的内容
    if (nameInput && name && !nameInput.value.trim()) {
        nameInput.value = name;
        console.log('自动填充脚本名称:', name);
    }

    if (versionInput && version && !versionInput.value.trim()) {
        versionInput.value = version;
        console.log('自动填充版本号:', version);
    }
}

// 页面加载完成后自动初始化卡片
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，立即初始化权限卡片...');

    // 立即初始化权限卡片，无延迟
    initPermissionCards();

    // 延迟一点时间确保DOM完全渲染后再初始化编辑状态
    setTimeout(function() {
        console.log('开始初始化编辑模式...');

        // 初始化编辑模式的权限状态
        <?php if ($edit_script): ?>
            console.log('检测到编辑模式，开始设置权限状态');

            const wechatValue = <?php echo ($edit_script['has_wechat_store'] ?? 0) ? '1' : '0'; ?>;
            const douyinValue = <?php echo ($edit_script['has_douyin_store'] ?? 0) ? '1' : '0'; ?>;

            console.log('从数据库读取的权限值 - 微信小店:', wechatValue, '抖店:', douyinValue);

            // 设置隐藏输入框的值
            const wechatInput = document.getElementById('has_wechat_store');
            const douyinInput = document.getElementById('has_douyin_store');

            if (wechatInput) {
                wechatInput.value = wechatValue;
                console.log('✓ 设置微信小店输入框值:', wechatValue);
            } else {
                console.error('✗ 未找到微信小店输入框');
            }

            if (douyinInput) {
                douyinInput.value = douyinValue;
                console.log('✓ 设置抖店输入框值:', douyinValue);
            } else {
                console.error('✗ 未找到抖店输入框');
            }

            // 查找并设置卡片的选中状态
            const allCards = document.querySelectorAll('.permission-card-item');
            console.log('找到权限卡片数量:', allCards.length);

            allCards.forEach((card, index) => {
                const permission = card.getAttribute('data-permission');
                console.log(`卡片${index + 1}: ${permission}`);
            });

            // 设置卡片的选中状态
            if (wechatValue === '1') {
                const wechatCard = document.querySelector('[data-permission="wechat_store"]');
                if (wechatCard) {
                    wechatCard.classList.add('selected');
                    console.log('✓ 微信小店卡片设置为选中状态');
                } else {
                    console.error('✗ 未找到微信小店卡片');
                }
            }

            if (douyinValue === '1') {
                const douyinCard = document.querySelector('[data-permission="douyin_store"]');
                if (douyinCard) {
                    douyinCard.classList.add('selected');
                    console.log('✓ 抖店卡片设置为选中状态');
                } else {
                    console.error('✗ 未找到抖店卡片');
                }
            }

            console.log('编辑模式权限状态初始化完成');
        <?php else: ?>
            console.log('非编辑模式，跳过权限状态初始化');
        <?php endif; ?>

        // 绑定脚本代码输入框的事件监听器
        const scriptCodeTextarea = document.getElementById('scriptCode');
        if (scriptCodeTextarea) {
            // 检查是否为编辑模式
            const isEditMode = <?php echo $edit_script ? 'true' : 'false'; ?>;

            if (isEditMode) {
                console.log('编辑模式：禁用自动提取功能，避免覆盖现有脚本信息');
            } else {
                console.log('新增模式：启用脚本代码自动提取功能');

                // 监听输入事件（实时提取）
                scriptCodeTextarea.addEventListener('input', function() {
                    const code = this.value;
                    if (code.length > 50) { // 只有当代码长度足够时才提取
                        const info = extractScriptInfo(code);
                        if (info.name || info.version) {
                            updateFormFields(info.name, info.version);
                        }
                    }
                });

                // 监听粘贴事件（立即提取）
                scriptCodeTextarea.addEventListener('paste', function() {
                    // 延迟一点时间等待粘贴内容完成
                    setTimeout(() => {
                        const code = this.value;
                        const info = extractScriptInfo(code);
                        if (info.name || info.version) {
                            updateFormFields(info.name, info.version);
                            console.log('从粘贴的代码中提取信息:', info);
                        }
                    }, 100);
                });

                // 监听失去焦点事件（最终提取）
                scriptCodeTextarea.addEventListener('blur', function() {
                    const code = this.value;
                    const info = extractScriptInfo(code);
                    if (info.name || info.version) {
                        updateFormFields(info.name, info.version);
                    }
                });
            }
        }
    }, 300); // 增加延迟时间到300ms
});



// 简单的提示函数
function showToast(message, type = 'info') {
    const toast = document.createElement('div');

    let backgroundColor, borderColor;
    switch(type) {
        case 'success':
            backgroundColor = 'rgba(34, 197, 94, 0.9)';
            borderColor = '#22c55e';
            break;
        case 'error':
            backgroundColor = 'rgba(239, 68, 68, 0.9)';
            borderColor = '#ef4444';
            break;
        default:
            backgroundColor = 'rgba(59, 130, 246, 0.9)';
            borderColor = '#3b82f6';
    }

    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        border-left: 4px solid ${borderColor};
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-size: 14px;
        max-width: 300px;
        word-wrap: break-word;
    `;

    toast.textContent = message;
    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}



console.log('所有全局函数已定义完成');
</script>

<div class="scripts-container">
    <div class="card">
        <h2><i class="fas fa-plus-circle"></i> <?php echo $edit_script ? '编辑脚本' : '添加脚本'; ?></h2>
        

        
        <?php echo $message; ?>
        
        <form method="POST" id="scriptForm">
            <?php if ($edit_script): ?>
                <input type="hidden" name="script_id" value="<?php echo $edit_script['id']; ?>">
            <?php endif; ?>
            
            <div class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-tag"></i> 脚本名称</label>
                    <input type="text" name="name" id="scriptName" value="<?php echo htmlspecialchars($edit_script['name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>" placeholder="例如：小梅花AI智能客服">
                </div>
                <div class="form-group">
                    <label><i class="fas fa-code-branch"></i> 版本号</label>
                    <input type="text" name="version" id="scriptVersion" value="<?php echo htmlspecialchars($edit_script['version'] ?? '', ENT_QUOTES, 'UTF-8'); ?>" placeholder="例如：1.0.0">
                </div>
            </div>
            
            <div class="form-group">
                <label><i class="fas fa-info-circle"></i> 脚本描述 (可选)</label>
                <input type="text" name="description" value="<?php echo htmlspecialchars($edit_script['description'] ?? '', ENT_QUOTES, 'UTF-8'); ?>" placeholder="简单描述脚本功能">
            </div>

            <!-- 脚本类型设置 -->
            <div class="form-group">
                <label><i class="fas fa-cogs"></i> 脚本类型</label>



                <div class="permission-cards-wrapper">
                    <div class="permission-card-item" data-permission="wechat_store">
                        <div class="permission-card-box">
                            <div class="permission-card-title">小梅花AI客服</div>
                            <div class="permission-card-subtitle">微信小店</div>
                        </div>
                    </div>
                    <div class="permission-card-item" data-permission="douyin_store">
                        <div class="permission-card-box">
                            <div class="permission-card-title">小梅花AI客服</div>
                            <div class="permission-card-subtitle">抖店</div>
                        </div>
                    </div>
                </div>

                <!-- 隐藏的input用于表单提交 -->
                <input type="hidden" name="has_wechat_store" id="has_wechat_store" value="0">
                <input type="hidden" name="has_douyin_store" id="has_douyin_store" value="0">
            </div>

            <div class="form-group">
                <label><i class="fas fa-code"></i> 脚本代码</label>
                <textarea name="script_code" id="scriptCode" rows="10" required placeholder="在此粘贴您的JavaScript代码或完整的UserScript..."><?php echo htmlspecialchars($edit_script['script_code'] ?? '', ENT_QUOTES, 'UTF-8'); ?></textarea>
            </div>
            
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 10px;">
                <button type="submit" name="save_script" class="btn btn-primary">
                    <i class="fas fa-save"></i> <?php echo $edit_script ? '更新脚本' : '保存脚本'; ?>
                </button>
                
                <?php if ($edit_script): ?>
                    <a href="index.php?page=scripts" class="btn btn-secondary">
                        <i class="fas fa-times"></i> 取消编辑
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <div class="card">
        <h2><i class="fas fa-list"></i> 脚本管理</h2>
        
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
            <div style="color: rgba(255,255,255,0.8); font-size: 14px;">
                <i class="fas fa-info-circle"></i>
                脚本列表 (共 <span id="scriptCount"><?php echo count($scripts); ?></span> 个脚本)
            </div>

            <!-- 脚本类型筛选按钮 -->
            <div style="display: flex; gap: 10px; align-items: center;">
                <button type="button" onclick="filterScripts('all')" id="filterAll" class="filter-btn active" style="padding: 8px 15px; background: rgba(0, 123, 255, 0.8); color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">
                    <i class="fas fa-list"></i> 全部
                </button>
                <button type="button" onclick="filterScripts('wechat')" id="filterWechat" class="filter-btn" style="padding: 8px 15px; background: rgba(255, 255, 255, 0.1); color: rgba(255,255,255,0.8); border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 5px; cursor: pointer; font-size: 14px;">
                    <i class="fas fa-store"></i> 微信小店
                </button>
                <button type="button" onclick="filterScripts('douyin')" id="filterDouyin" class="filter-btn" style="padding: 8px 15px; background: rgba(255, 255, 255, 0.1); color: rgba(255,255,255,0.8); border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 5px; cursor: pointer; font-size: 14px;">
                    <i class="fas fa-shopping-cart"></i> 抖店
                </button>
            </div>
        </div>
        
        <?php if (empty($scripts)): ?>
            <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 20px;">
                <i class="fas fa-info-circle"></i> 暂无脚本数据，请先添加脚本
            </p>
        <?php else: ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>

                            <th style="width: 35%;"><i class="fas fa-tag"></i> 脚本信息</th>
                            <th style="width: 10%; text-align: center;"><i class="fas fa-code-branch"></i> 版本</th>
                            <th style="width: 15%; text-align: center;"><i class="fas fa-shield-alt"></i> 脚本类型</th>
                            <th style="width: 8%; text-align: center;"><i class="fas fa-link"></i> 关联</th>
                            <th style="width: 15%;"><i class="fas fa-calendar"></i> 创建时间</th>
                            <th style="width: 17%; text-align: center;"><i class="fas fa-cogs"></i> 操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($scripts as $script): ?>
                        <tr class="script-row"
                            data-wechat="<?php echo $script['has_wechat_store'] ? '1' : '0'; ?>"
                            data-douyin="<?php echo $script['has_douyin_store'] ? '1' : '0'; ?>"
                            data-permission="<?php echo $script['has_wechat_store'] ? 'wechat' : ($script['has_douyin_store'] ? 'douyin' : 'none'); ?>">

                            <td>
                                <div class="script-name"><?php echo htmlspecialchars($script['name'], ENT_QUOTES, 'UTF-8'); ?></div>
                                <?php if (!empty($script['description'])): ?>
                                    <div class="script-description"><?php echo htmlspecialchars($script['description'], ENT_QUOTES, 'UTF-8'); ?></div>
                                <?php endif; ?>
                            </td>
                            <td style="text-align: center;">
                                <span class="version-badge"><?php echo htmlspecialchars($script['version'], ENT_QUOTES, 'UTF-8'); ?></span>
                            </td>
                            <td style="text-align: center;">
                                <div class="permission-badges">
                                    <?php if ($script['has_wechat_store']): ?>
                                        <span class="permission-badge wechat-store" title="支持微信小店">
                                            <i class="fas fa-store"></i> 微信小店
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($script['has_douyin_store']): ?>
                                        <span class="permission-badge douyin-store" title="支持抖店">
                                            <i class="fas fa-shopping-cart"></i> 抖店
                                        </span>
                                    <?php endif; ?>
                                    <?php if (!$script['has_wechat_store'] && !$script['has_douyin_store']): ?>
                                        <span style="opacity: 0.5; font-size: 12px;">无类型</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td style="text-align: center;">
                                <?php if ($script['linked_keys_count'] > 0): ?>
                                    <span class="count-badge"><?php echo $script['linked_keys_count']; ?></span>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">0</span>
                                <?php endif; ?>
                            </td>

                            <td>
                                <?php echo date('Y-m-d H:i', strtotime($script['created_at'])); ?>
                            </td>
                            <td style="text-align: center;">
                                <div class="action-buttons">
                                    <a href="index.php?page=scripts&edit_script=<?php echo $script['id']; ?>"
                                       class="btn btn-secondary"
                                       title="编辑">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>

                                    <a href="index.php?page=scripts&delete_script=<?php echo $script['id']; ?>"
                                       class="btn btn-danger"
                                       title="删除"
                                       onclick="return confirm('确定要删除这个脚本吗？<?php echo $script['linked_keys_count'] > 0 ? '\\n注意：此脚本关联了 ' . $script['linked_keys_count'] . ' 个卡密！' : ''; ?>')">
                                        <i class="fas fa-trash"></i> 删除
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>


</div>


