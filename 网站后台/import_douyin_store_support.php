<?php
/**
 * 抖店支持功能导入脚本
 * 用于安全导入09_database_douyin_store_support.sql
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置执行时间限制
set_time_limit(300);

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'xiaomeihuakefu_c',
    'username' => 'xiaomeihuakefu_c',
    'password' => '7Da5F1Xx995cxYz8',
    'charset' => 'utf8mb4'
];

// 日志记录函数
function logMessage($message, $type = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    echo "[{$timestamp}] [{$type}] {$message}\n";
    
    // 同时写入日志文件
    $logFile = __DIR__ . '/import_douyin_store.log';
    file_put_contents($logFile, "[{$timestamp}] [{$type}] {$message}\n", FILE_APPEND | LOCK_EX);
}

try {
    logMessage("开始导入抖店支持功能...");
    
    // 创建数据库连接
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    logMessage("数据库连接成功");
    
    // 检查当前数据库结构
    logMessage("检查当前数据库结构...");
    
    // 检查license_keys表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'license_keys'");
    if ($stmt->rowCount() == 0) {
        throw new Exception("license_keys表不存在，请先运行基础数据库安装脚本");
    }
    
    // 检查license_key_stores表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'license_key_stores'");
    if ($stmt->rowCount() == 0) {
        throw new Exception("license_key_stores表不存在，请先运行基础数据库安装脚本");
    }
    
    logMessage("基础表结构检查通过");
    
    // 检查是否已经添加了抖店字段
    $stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'douyin_store_name'");
    if ($stmt->rowCount() > 0) {
        logMessage("检测到抖店字段已存在，跳过字段添加", "WARNING");
        $fieldsExist = true;
    } else {
        $fieldsExist = false;
    }
    
    // 读取SQL文件
    $sqlFile = __DIR__ . '/09_database_douyin_store_support.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: $sqlFile");
    }
    logMessage("使用抖店支持SQL文件");
    
    $sql = file_get_contents($sqlFile);
    logMessage("SQL文件读取成功");
    
    // 分割SQL语句，过滤注释和空行
    $statements = [];
    $lines = explode("\n", $sql);
    $currentStatement = '';
    
    foreach ($lines as $line) {
        $line = trim($line);
        
        // 跳过注释和空行
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        
        $currentStatement .= $line . ' ';
        
        // 如果行以分号结尾，表示语句结束
        if (substr($line, -1) === ';') {
            $statements[] = trim($currentStatement);
            $currentStatement = '';
        }
    }
    
    logMessage("解析到 " . count($statements) . " 条SQL语句");
    
    $successCount = 0;
    $errorCount = 0;
    $skippedCount = 0;
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        foreach ($statements as $index => $statement) {
            if (empty($statement)) {
                continue;
            }
            
            // 如果字段已存在，跳过ALTER TABLE语句
            if ($fieldsExist && stripos($statement, 'ALTER TABLE') === 0) {
                logMessage("跳过语句 " . ($index + 1) . " (字段已存在): " . substr($statement, 0, 50) . "...", "SKIP");
                $skippedCount++;
                continue;
            }
            
            try {
                // 特殊处理SELECT语句（跳过，因为它们只是用于验证）
                if (stripos($statement, 'SELECT') === 0) {
                    logMessage("跳过验证查询语句 " . ($index + 1) . ": " . substr($statement, 0, 50) . "...", "SKIP");
                    $skippedCount++;
                    continue;
                }

                $pdo->exec($statement);
                $successCount++;
                logMessage("语句 " . ($index + 1) . " 执行成功: " . substr($statement, 0, 50) . "...");

            } catch (PDOException $e) {
                // 检查是否是字段已存在的错误
                if (strpos($e->getMessage(), 'Duplicate column name') !== false ||
                    strpos($e->getMessage(), 'column already exists') !== false) {
                    logMessage("跳过语句 " . ($index + 1) . " (字段已存在): " . $e->getMessage(), "SKIP");
                    $skippedCount++;
                    continue;
                }

                // 检查是否是索引已存在的错误
                if (strpos($e->getMessage(), 'Duplicate key name') !== false ||
                    strpos($e->getMessage(), 'already exists') !== false) {
                    logMessage("跳过语句 " . ($index + 1) . " (索引已存在): " . $e->getMessage(), "SKIP");
                    $skippedCount++;
                    continue;
                }

                // 检查是否是视图已存在的错误
                if (strpos($e->getMessage(), 'already exists') !== false &&
                    stripos($statement, 'CREATE VIEW') !== false) {
                    logMessage("跳过语句 " . ($index + 1) . " (视图已存在): " . $e->getMessage(), "SKIP");
                    $skippedCount++;
                    continue;
                }

                $errorCount++;
                logMessage("语句 " . ($index + 1) . " 执行失败: " . $e->getMessage(), "ERROR");
                logMessage("失败的SQL: " . $statement, "ERROR");

                // 如果是关键的ALTER TABLE语句失败，抛出异常
                if (stripos($statement, 'ALTER TABLE') === 0 &&
                    strpos($e->getMessage(), 'Duplicate column name') === false) {
                    throw $e;
                }
            }
        }
        
        // 提交事务
        $pdo->commit();
        logMessage("事务提交成功");
        
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollback();
        throw new Exception("事务执行失败，已回滚: " . $e->getMessage());
    }
    
    logMessage(str_repeat("=", 50));
    logMessage("导入结果统计:");
    logMessage("✅ 成功: $successCount 条");
    logMessage("⚠️  跳过: $skippedCount 条");
    logMessage("❌ 失败: $errorCount 条");
    logMessage("📝 总计: " . ($successCount + $skippedCount + $errorCount) . " 条");
    
    if ($errorCount === 0) {
        logMessage("🎉 抖店支持功能导入成功！", "SUCCESS");
    } else {
        logMessage("⚠️  部分语句执行失败，但核心功能已安装", "WARNING");
    }
    
    // 验证安装结果
    logMessage(str_repeat("=", 50));
    logMessage("验证安装结果:");
    
    // 检查新增字段
    $fields = ['douyin_store_name', 'douyin_store_id'];
    foreach ($fields as $field) {
        $stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE '$field'");
        if ($stmt->rowCount() > 0) {
            logMessage("✅ license_keys表字段 '$field' 存在");
        } else {
            logMessage("❌ license_keys表字段 '$field' 不存在", "ERROR");
        }
    }
    
    // 检查license_key_stores表的新字段
    $storeFields = ['store_type', 'douyin_store_name', 'douyin_store_id'];
    foreach ($storeFields as $field) {
        $stmt = $pdo->query("SHOW COLUMNS FROM license_key_stores LIKE '$field'");
        if ($stmt->rowCount() > 0) {
            logMessage("✅ license_key_stores表字段 '$field' 存在");
        } else {
            logMessage("❌ license_key_stores表字段 '$field' 不存在", "ERROR");
        }
    }
    
    // 检查视图是否创建成功
    $stmt = $pdo->query("SHOW TABLES LIKE 'v_license_keys_with_stores'");
    if ($stmt->rowCount() > 0) {
        logMessage("✅ 视图 'v_license_keys_with_stores' 创建成功");
    } else {
        logMessage("❌ 视图 'v_license_keys_with_stores' 未找到", "WARNING");
    }
    
    // 检查索引
    $stmt = $pdo->query("SHOW INDEX FROM license_keys WHERE Key_name = 'idx_license_keys_douyin_store_id'");
    if ($stmt->rowCount() > 0) {
        logMessage("✅ 索引 'idx_license_keys_douyin_store_id' 创建成功");
    } else {
        logMessage("❌ 索引 'idx_license_keys_douyin_store_id' 未找到", "WARNING");
    }
    
    logMessage("✨ 导入脚本执行完成！", "SUCCESS");
    
} catch (Exception $e) {
    logMessage("导入失败: " . $e->getMessage(), "ERROR");
    logMessage("详细错误信息: " . $e->getTraceAsString(), "ERROR");
    exit(1);
}
?>
