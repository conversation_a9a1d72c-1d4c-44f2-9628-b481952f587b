-- =====================================================
-- 数据库升级脚本：抖店支持功能
-- 版本：v1.2 (简化版)
-- 日期：2025-08-15
-- 描述：为卡密管理系统添加抖店店铺支持
-- 说明：由于字段已存在，此脚本主要用于文档记录
-- =====================================================

-- 注意：以下字段已在数据库中存在，无需重复执行
-- 如需重新安装，请使用 fix_douyin_import.php 脚本

-- 1. license_keys 表的抖店字段（已存在）
-- ALTER TABLE license_keys ADD COLUMN douyin_store_name VARCHAR(255) DEFAULT NULL;
-- ALTER TABLE license_keys ADD COLUMN douyin_store_id VARCHAR(100) DEFAULT NULL;

-- 2. license_key_stores 表的抖店字段（已存在）
-- ALTER TABLE license_key_stores ADD COLUMN store_type VARCHAR(20) DEFAULT 'wechat';
-- ALTER TABLE license_key_stores ADD COLUMN douyin_store_name VARCHAR(255) DEFAULT NULL;
-- ALTER TABLE license_key_stores ADD COLUMN douyin_store_id VARCHAR(100) DEFAULT NULL;

-- 3. 索引（已存在）
-- CREATE INDEX idx_license_keys_douyin_store_id ON license_keys(douyin_store_id);
-- CREATE INDEX idx_license_key_stores_store_type ON license_key_stores(store_type);
-- CREATE INDEX idx_license_key_stores_douyin_store_id ON license_key_stores(douyin_store_id);

-- 4. 数据更新（已完成）
-- UPDATE license_key_stores SET store_type = 'wechat' WHERE store_type IS NULL OR store_type = '';

-- 5. 视图（已存在）
-- DROP VIEW IF EXISTS v_license_keys_with_stores;
-- CREATE VIEW v_license_keys_with_stores AS
-- SELECT
--     lk.id,
--     lk.key_value,
--     lk.type,
--     lk.status,
--     lk.expiry_date,
--     lk.has_customer_service,
--     lk.has_product_listing,
--     lk.is_multi_store,
--     lk.created_at,
--     -- 微信店铺信息
--     lk.store_name as wechat_store_name,
--     lk.wechat_store_id,
--     -- 抖店信息
--     lk.douyin_store_name,
--     lk.douyin_store_id,
--     -- 额外店铺数量
--     (SELECT COUNT(*) FROM license_key_stores lks WHERE lks.license_key_id = lk.id) as additional_stores_count
-- FROM license_keys lk;

-- =====================================================
-- 抖店支持功能已完整安装
--
-- 已实现功能：
-- 1. ✅ 抖店店铺信息存储字段
-- 2. ✅ 多店铺类型支持（微信/抖店）
-- 3. ✅ 数据查询视图优化
-- 4. ✅ 索引性能优化
-- 5. ✅ 数据完整性保证
--
-- 当前状态：
-- - 所有字段已创建并使用VARCHAR类型
-- - 所有索引已创建并正常工作
-- - 视图已创建并可正常查询
-- - 数据已更新并保持一致性
--
-- 如需重新安装，请使用：php fix_douyin_import.php
-- =====================================================
