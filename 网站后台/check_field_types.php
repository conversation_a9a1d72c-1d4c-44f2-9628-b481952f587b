<?php
/**
 * 检查数据库字段类型脚本
 */

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'xiaomeihuakefu_c',
    'username' => 'xiaomeihuakefu_c',
    'password' => '7Da5F1Xx995cxYz8',
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "=== license_keys 表字段信息 ===\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM license_keys WHERE Field LIKE '%douyin%'");
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $col) {
        echo "字段: {$col['Field']}, 类型: {$col['Type']}, 默认值: {$col['Default']}\n";
    }
    
    echo "\n=== license_key_stores 表字段信息 ===\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM license_key_stores WHERE Field LIKE '%douyin%' OR Field = 'store_type'");
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $col) {
        echo "字段: {$col['Field']}, 类型: {$col['Type']}, 默认值: {$col['Default']}\n";
    }
    
    echo "\n=== 现有索引信息 ===\n";
    $stmt = $pdo->query("SHOW INDEX FROM license_keys WHERE Key_name LIKE '%douyin%'");
    $indexes = $stmt->fetchAll();
    
    if (empty($indexes)) {
        echo "license_keys表没有抖店相关索引\n";
    } else {
        foreach ($indexes as $idx) {
            echo "表: {$idx['Table']}, 索引: {$idx['Key_name']}, 字段: {$idx['Column_name']}\n";
        }
    }
    
    $stmt = $pdo->query("SHOW INDEX FROM license_key_stores WHERE Key_name LIKE '%douyin%' OR Key_name LIKE '%store_type%'");
    $indexes = $stmt->fetchAll();
    
    if (empty($indexes)) {
        echo "license_key_stores表没有抖店相关索引\n";
    } else {
        foreach ($indexes as $idx) {
            echo "表: {$idx['Table']}, 索引: {$idx['Key_name']}, 字段: {$idx['Column_name']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
