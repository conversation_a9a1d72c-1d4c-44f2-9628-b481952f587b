<?php
/**
 * 数据库连接诊断工具
 * 
 * 用于检查数据库连接问题并提供解决方案
 * 上传到服务器运行后，可以显示可能的问题原因
 */

// 禁用错误显示，避免敏感信息泄露
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 将错误信息记录到专用日志文件
ini_set('error_log', dirname(__FILE__) . '/db_connection_errors.log');

// 设置输出内容类型
header('Content-Type: text/html; charset=utf-8');
echo "<html><head><title>数据库连接诊断工具</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
    h1 { color: #333; }
    .container { max-width: 800px; margin: 0 auto; background: #f9f9f9; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
    .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; }
    .error { color: red; background: #ffebeb; padding: 10px; border-radius: 5px; }
    .warning { color: orange; background: #fff8e6; padding: 10px; border-radius: 5px; }
    .info { color: blue; background: #e6f2ff; padding: 10px; border-radius: 5px; }
    pre { background: #eee; padding: 10px; border-radius: 5px; overflow: auto; }
</style>";
echo "</head><body><div class='container'>";
echo "<h1>数据库连接诊断工具</h1>";

// 检查环境变量和文件
echo "<h2>1. 环境文件检查</h2>";
$env_file_parent = dirname(dirname(__FILE__)) . '/.env';
$env_file_local = dirname(__FILE__) . '/.env';

if (file_exists($env_file_parent)) {
    echo "<p class='success'>✓ 上级目录中的.env文件存在</p>";
    $env_content = file_get_contents($env_file_parent);
    echo "<p>文件路径: " . htmlspecialchars($env_file_parent) . "</p>";
} elseif (file_exists($env_file_local)) {
    echo "<p class='success'>✓ 当前目录中的.env文件存在</p>";
    $env_content = file_get_contents($env_file_local);
    echo "<p>文件路径: " . htmlspecialchars($env_file_local) . "</p>";
} else {
    echo "<p class='error'>✗ .env文件不存在</p>";
    echo "<p>已检查路径:</p><ul>";
    echo "<li>" . htmlspecialchars($env_file_parent) . "</li>";
    echo "<li>" . htmlspecialchars($env_file_local) . "</li>";
    echo "</ul>";
    $env_content = '';
}

// 尝试解析.env文件内容
echo "<h2>2. 环境变量内容检查</h2>";
if (!empty($env_content)) {
    $env_vars = [];
    $lines = explode("\n", $env_content);
    
    foreach ($lines as $line) {
        $line = trim($line);
        
        // 跳过注释和空行
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        // 解析KEY=VALUE格式
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);
            
            // 移除引号
            if (strpos($value, '"') === 0 && strrpos($value, '"') === strlen($value) - 1) {
                $value = substr($value, 1, -1);
            }
            if (strpos($value, "'") === 0 && strrpos($value, "'") === strlen($value) - 1) {
                $value = substr($value, 1, -1);
            }
            
            $env_vars[$key] = $value;
        }
    }
    
    // 检查数据库连接信息
    $required_db_vars = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASS'];
    $missing_vars = [];
    
    foreach ($required_db_vars as $var) {
        if (!isset($env_vars[$var]) || empty($env_vars[$var])) {
            if ($var !== 'DB_PASS') {  // 密码可以为空
                $missing_vars[] = $var;
            }
        }
    }
    
    if (!empty($missing_vars)) {
        echo "<p class='error'>✗ .env文件缺少关键数据库变量: " . implode(', ', $missing_vars) . "</p>";
    } else {
        echo "<p class='success'>✓ .env文件包含所有必要的数据库变量</p>";
        
        // 显示数据库连接信息（隐藏密码）
        echo "<pre>";
        echo "DB_HOST: " . htmlspecialchars($env_vars['DB_HOST']) . "\n";
        echo "DB_PORT: " . htmlspecialchars($env_vars['DB_PORT']) . "\n";
        echo "DB_NAME: " . htmlspecialchars($env_vars['DB_NAME']) . "\n";
        echo "DB_USER: " . htmlspecialchars($env_vars['DB_USER']) . "\n";
        echo "DB_PASS: " . (empty($env_vars['DB_PASS']) ? "(空)" : "******") . "\n";
        echo "</pre>";
    }
} else {
    echo "<p class='warning'>⚠ 无法读取.env文件内容</p>";
}

// 检查数据库连接
echo "<h2>3. 数据库连接测试</h2>";

// 尝试使用PDO连接数据库
$db_connections = [];

// 从.env文件读取连接信息
if (!empty($env_content)) {
    $db_connections[] = [
        'name' => '.env配置',
        'host' => $env_vars['DB_HOST'] ?? 'localhost',
        'port' => $env_vars['DB_PORT'] ?? '3306',
        'dbname' => $env_vars['DB_NAME'] ?? '',
        'user' => $env_vars['DB_USER'] ?? '',
        'pass' => $env_vars['DB_PASS'] ?? '',
    ];
}

// 添加您提供的固定信息作为备用连接方式
$db_connections[] = [
    'name' => '固定参数',
    'host' => 'localhost',
    'port' => '3306',
    'dbname' => 'www_xiaomeihuake',
    'user' => 'www_xiaomeihuake',
    'pass' => 'hF623sd8YwjnkBKj',
];

foreach ($db_connections as $connection) {
    echo "<h3>测试连接: " . htmlspecialchars($connection['name']) . "</h3>";
    
    try {
        $dsn = "mysql:host={$connection['host']};port={$connection['port']};dbname={$connection['dbname']};charset=utf8mb4";
        $pdo = new PDO($dsn, $connection['user'], $connection['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5,
        ]);
        
        echo "<p class='success'>✓ 数据库连接成功!</p>";
        
        // 尝试执行简单查询
        try {
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "<p class='success'>✓ 成功执行查询，找到 " . count($tables) . " 个表</p>";
            
            if (count($tables) > 0) {
                echo "<p>表列表:</p>";
                echo "<pre>" . implode("\n", array_map('htmlspecialchars', $tables)) . "</pre>";
            }
            
            // 检查特定的系统表
            $required_tables = ['admin_users', 'api_keys', 'jwt_tokens'];
            $missing_tables = array_diff($required_tables, $tables);
            
            if (!empty($missing_tables)) {
                echo "<p class='warning'>⚠ 缺少一些系统表: " . implode(', ', $missing_tables) . "</p>";
            } else {
                echo "<p class='success'>✓ 所有必要的系统表都存在</p>";
            }
            
        } catch (PDOException $e) {
            echo "<p class='error'>✗ 查询失败: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'>✗ 连接失败: " . htmlspecialchars($e->getMessage()) . "</p>";
        
        // 提供可能的解决方案
        $error_msg = $e->getMessage();
        if (strpos($error_msg, 'Access denied') !== false) {
            echo "<p class='info'>可能的原因: 用户名或密码错误，或者该用户没有权限访问指定的数据库</p>";
            echo "<p class='info'>建议: 检查用户名和密码，确认该用户有权限访问数据库</p>";
        } elseif (strpos($error_msg, "Unknown database") !== false) {
            echo "<p class='info'>可能的原因: 数据库不存在</p>";
            echo "<p class='info'>建议: 创建数据库或检查数据库名称是否正确</p>";
        } elseif (strpos($error_msg, "Connection refused") !== false) {
            echo "<p class='info'>可能的原因: 数据库服务器未运行或网络问题</p>";
            echo "<p class='info'>建议: 检查数据库服务器是否运行，确认服务器地址和端口是否正确</p>";
        }
    }
}

// 检查环境变量管理器
echo "<h2>4. 环境管理器检查</h2>";
$env_manager_file = dirname(__FILE__) . '/api/env_manager.php';

if (file_exists($env_manager_file)) {
    echo "<p class='success'>✓ 环境变量管理器文件存在</p>";
    
    // 检查文件权限
    $perms = fileperms($env_manager_file);
    
    echo "<p>文件权限: " . substr(sprintf('%o', $perms), -4) . "</p>";
    
    // 显示PHP和Web服务器用户
    echo "<p>当前PHP运行用户: " . exec('whoami') . "</p>";
} else {
    echo "<p class='error'>✗ 环境变量管理器文件不存在: " . htmlspecialchars($env_manager_file) . "</p>";
}

// 显示PHP版本和PDO信息
echo "<h2>5. PHP环境信息</h2>";
echo "<p>PHP版本: " . phpversion() . "</p>";
echo "<p>PDO驱动: " . (extension_loaded('pdo_mysql') ? '已安装' : '未安装') . "</p>";

// 检查PHP错误日志
echo "<h2>6. 错误日志检查</h2>";
$error_log_file = ini_get('error_log');

if (!empty($error_log_file) && file_exists($error_log_file)) {
    echo "<p class='success'>✓ PHP错误日志文件存在: " . htmlspecialchars($error_log_file) . "</p>";
    
    $log_content = shell_exec("tail -n 20 " . escapeshellarg($error_log_file));
    if (!empty($log_content)) {
        echo "<p>最近的错误日志:</p>";
        echo "<pre>" . htmlspecialchars($log_content) . "</pre>";
    } else {
        echo "<p class='info'>错误日志为空或无法读取</p>";
    }
} else {
    echo "<p class='warning'>⚠ PHP错误日志文件未配置或不存在</p>";
}

// 提供解决方案
echo "<h2>7. 建议的解决方案</h2>";
echo "<ol>";
echo "<li>确保.env文件存在且包含正确的数据库连接信息</li>";
echo "<li>确认数据库服务器正在运行且可访问</li>";
echo "<li>检查数据库用户名和密码是否正确</li>";
echo "<li>确认数据库用户有权限访问指定的数据库</li>";
echo "<li>如果使用宝塔面板，请确认数据库已创建且用户已绑定到数据库</li>";
echo "<li>检查网站根目录和文件权限是否正确</li>";
echo "</ol>";

echo "<h2>8. 创建.env文件</h2>";
echo "<p>如果您需要创建.env文件，请将以下内容保存到网站根目录下的.env文件中：</p>";
echo "<pre>";
echo "# 数据库配置\n";
echo "DB_HOST=localhost\n";
echo "DB_PORT=3306\n";
echo "DB_NAME=www_xiaomeihuake\n";
echo "DB_USER=www_xiaomeihuake\n";
echo "DB_PASS=hF623sd8YwjnkBKj\n";
echo "\n";
echo "# API配置\n";
echo "API_JWT_SECRET=xiaomeihua_jwt_secret_key_2023\n";
echo "API_RATE_LIMIT=100";
echo "</pre>";

echo "</div></body></html>";
?> 